# VoxStudent v1.0.0

A comprehensive student management system with facial recognition attendance, WhatsApp integration, and complete administrative portal.

## 🚀 Features

- **Passwordless Authentication** - Magic links via email/WhatsApp
- **Facial Recognition** - Automated attendance using face-api.js
- **WhatsApp Integration** - Automated reminders and notifications
- **Course Management** - Complete CRUD for courses, classes, and lessons
- **Student Portal** - Lesson retakes, makeup classes, and mentoring
- **Mentoring System** - 1:1 sessions with Google Calendar integration
- **Real-time Analytics** - Dashboard with attendance and engagement metrics
- **Auto-save Forms** - All forms save automatically with visual feedback
- **Recovery System** - Automated makeup class scheduling
- **LGPD Compliant** - Brazilian data protection law compliance

## 🛠️ Tech Stack

- **Frontend**: Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS v4
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQLite (development), PostgreSQL (production)
- **Authentication**: JWT with HTTP-only cookies
- **Facial Recognition**: face-api.js
- **WhatsApp**: whatsapp-web.js
- **Calendar**: Google Calendar API
- **Email**: Nodemailer with SMTP
- **Testing**: Jest (unit), Playwright (E2E)
- **Deployment**: Docker, Docker Compose

## 🏃‍♂️ Quick Start

### Prerequisites

- Node.js 18+ and npm
- SQLite (dev) or PostgreSQL (prod)
- SMTP server for emails
- WhatsApp account for integration

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vox-student-nextjs
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Setup database**
   ```bash
   npm run db:generate
   npm run db:push
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

Visit [http://localhost:3000](http://localhost:3000) to access the application.

## 📋 Development Commands

### Core Commands
```bash
npm run dev                    # Start development server
npm run build                 # Build for production
npm run start                 # Start production server
npm run lint                  # Run ESLint
npm run typecheck            # Run TypeScript type checking
```

### Database Commands
```bash
npm run db:generate          # Generate Prisma client
npm run db:push             # Push schema changes to database
npm run db:migrate          # Run database migrations
npm run db:studio           # Open Prisma Studio
```

### Testing Commands
```bash
npm run test                # Run unit tests with Jest
npm run test:coverage       # Run tests with coverage
npm run test:e2e           # Run Playwright E2E tests
npm run test:e2e:qa        # Run QA-specific E2E tests
```

### QA & Release Commands
```bash
npm run qa:setup           # Set up QA environment with Docker
npm run qa:test            # Run full QA test suite
npm run qa:teardown        # Tear down QA environment
npm run pre-release        # Run complete pre-release QA checks
```

## 🏗️ Project Structure

```
src/
├── app/                   # Next.js App Router pages and API routes
│   ├── admin/            # Admin-only pages
│   ├── api/              # REST API endpoints
│   ├── auth/             # Authentication pages
│   └── recovery/         # Student lesson recovery/retake
├── components/           # React components
│   ├── ui/               # Reusable UI components (shadcn/ui)
│   ├── layouts/          # Layout components
│   └── navigation/       # Navigation components
├── lib/                  # Shared utilities and services
│   ├── services/         # Business logic services
│   ├── auth.ts          # Authentication utilities
│   ├── prisma.ts        # Database client
│   └── utils.ts         # Common utilities
├── contexts/            # React contexts
├── hooks/               # Custom React hooks
└── types/               # TypeScript type definitions

docs/                    # Documentation
├── user-manual/         # User guides in Portuguese
├── development/         # Developer documentation
├── deployment/          # Deploy and operations guides
├── security/            # Security and compliance docs
└── testing/             # Testing documentation

tests/                   # Test suites
├── e2e/                # Playwright E2E tests
└── unit/               # Jest unit tests
```

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example`):

```bash
# Database
DATABASE_URL="file:./dev.db"

# Authentication
JWT_SECRET="your-jwt-secret"
NEXTAUTH_SECRET="your-nextauth-secret"

# Email (SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# WhatsApp (optional)
WHATSAPP_ENABLED=true

# Google Calendar (optional)
GOOGLE_CALENDAR_ENABLED=false
```

### Tailwind CSS v4 Configuration

This project uses Tailwind CSS v4 with modern configuration:
- `@import "tailwindcss"` in `globals.css`
- Content sources defined with `@source "./src/**/*.{js,ts,jsx,tsx,mdx}"`
- PostCSS configured with `@tailwindcss/postcss` and `autoprefixer`

## 🚢 Deployment

### Docker Deployment

1. **Build and run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

2. **Production deployment**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Set up production database**
   ```bash
   npm run db:migrate
   ```

3. **Start production server**
   ```bash
   npm run start
   ```

## 📚 Documentation

- **[User Manual](./docs/user-manual/)** - Complete guide in Portuguese
- **[Development Guide](./docs/development/)** - Architecture and contribution
- **[API Reference](./docs/development/api-reference.md)** - Complete API documentation
- **[Deployment Guide](./docs/deployment/)** - Installation and configuration
- **[Security Guide](./docs/security/)** - LGPD compliance and best practices

## 🧪 Testing

### Unit Tests
```bash
npm run test                # Run all unit tests
npm run test:coverage       # Run with coverage report
```

### E2E Tests
```bash
npm run test:e2e           # Run Playwright tests
npm run test:e2e:qa        # Run QA-specific tests
```

### QA Environment
```bash
npm run qa:setup           # Set up Docker QA environment
npm run qa:test            # Run complete QA suite
npm run pre-release        # Full pre-release validation
```

## 🔒 Security & Compliance

- **LGPD Compliant** - Brazilian data protection law
- **Passwordless Authentication** - Magic links only
- **Secure Biometric Storage** - Face descriptors, not images
- **JWT with HTTP-only Cookies** - Secure session management
- **Audit Logging** - Complete action tracking
- **Rate Limiting** - API protection

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**
4. **Run tests and quality checks**
   ```bash
   npm run lint && npm run typecheck
   npm run test
   ```
5. **Commit your changes**
   ```bash
   git commit -m "feat: add amazing feature"
   ```
6. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
7. **Open a Pull Request**

### Development Guidelines

- Follow TypeScript best practices
- Use the established service layer pattern
- Maintain test coverage above 80%
- Follow conventional commit messages
- Always run `npm run lint && npm run typecheck` before committing

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

- **Documentation**: [docs/](./docs/)
- **Developer Guide**: [CLAUDE.md](./CLAUDE.md)
- **Issues**: Use the integrated logging system

---

**VoxStudent** - Modern educational management system with facial recognition and WhatsApp integration.
