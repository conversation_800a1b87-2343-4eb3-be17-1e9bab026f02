'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  MessageSquare, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  Eye
} from 'lucide-react';

interface NpsStats {
  pending: number;
  statistics: {
    pending: number;
    processing: number;
    sent: number;
    failed: number;
    cancelled: number;
  };
  recentMessages: Array<{
    id: string;
    recipientPhone: string;
    status: string;
    attempts: number;
    scheduledFor: string;
    sentAt?: string;
    errorMessage?: string;
    createdAt: string;
  }>;
}

interface CourseFeedbackStats {
  total: number;
  good: number;
  neutral: number;
  bad: number;
  goodPercentage: number;
  neutralPercentage: number;
  badPercentage: number;
}

export default function NpsAdminPage() {
  const [npsStats, setNpsStats] = useState<NpsStats | null>(null);
  const [coursesStats, setCoursesStats] = useState<Record<string, CourseFeedbackStats>>({});
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchNpsData();
  }, []);

  const fetchNpsData = async () => {
    try {
      setLoading(true);
      
      // Fetch NPS processing stats
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error('Sessão expirada');
        return;
      }

      const response = await fetch('/api/nps/process', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar dados NPS');
      }

      const data = await response.json();
      setNpsStats(data.data);

    } catch (error) {
      console.error('Error fetching NPS data:', error);
      toast.error('Erro ao carregar dados NPS');
    } finally {
      setLoading(false);
    }
  };

  const processNpsMessages = async () => {
    try {
      setProcessing(true);
      
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error('Sessão expirada');
        return;
      }

      const response = await fetch('/api/nps/process', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao processar mensagens NPS');
      }

      const data = await response.json();
      toast.success(data.message);
      
      // Refresh data
      await fetchNpsData();

    } catch (error) {
      console.error('Error processing NPS messages:', error);
      toast.error('Erro ao processar mensagens NPS');
    } finally {
      setProcessing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'processing': return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      case 'cancelled': return <AlertCircle className="w-4 h-4" />;
      default: return <MessageSquare className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const chartData = npsStats ? [
    { name: 'Enviadas', value: npsStats.statistics.sent, color: '#10b981' },
    { name: 'Pendentes', value: npsStats.statistics.pending, color: '#f59e0b' },
    { name: 'Falharam', value: npsStats.statistics.failed, color: '#ef4444' },
    { name: 'Processando', value: npsStats.statistics.processing, color: '#3b82f6' }
  ] : [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">NPS - Feedback das Aulas</h1>
          <p className="text-gray-600 mt-2">
            Gerencie e monitore o sistema de feedback dos alunos
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchNpsData} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Atualizar
          </Button>
          <Button 
            onClick={processNpsMessages} 
            disabled={processing}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {processing ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <MessageSquare className="w-4 h-4 mr-2" />
            )}
            Processar Mensagens
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensagens Pendentes</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{npsStats?.statistics.pending || 0}</div>
            <p className="text-xs text-gray-600">Aguardando envio</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensagens Enviadas</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{npsStats?.statistics.sent || 0}</div>
            <p className="text-xs text-gray-600">Enviadas com sucesso</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensagens Falharam</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{npsStats?.statistics.failed || 0}</div>
            <p className="text-xs text-gray-600">Erro no envio</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processando</CardTitle>
            <RefreshCw className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{npsStats?.statistics.processing || 0}</div>
            <p className="text-xs text-gray-600">Em processamento</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Distribuição de Status</CardTitle>
            <CardDescription>Status das mensagens NPS</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Mensagens por Status</CardTitle>
            <CardDescription>Quantidade de mensagens em cada status</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Messages */}
      <Card>
        <CardHeader>
          <CardTitle>Mensagens Recentes</CardTitle>
          <CardDescription>Últimas 10 mensagens NPS processadas</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {npsStats?.recentMessages.map((message) => (
              <div key={message.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(message.status)}
                  <div>
                    <p className="font-medium">{message.recipientPhone}</p>
                    <p className="text-sm text-gray-600">
                      Criado em {new Date(message.createdAt).toLocaleString('pt-BR')}
                    </p>
                    {message.errorMessage && (
                      <p className="text-sm text-red-600 mt-1">{message.errorMessage}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(message.status)}>
                    {message.status}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    Tentativas: {message.attempts}
                  </span>
                </div>
              </div>
            ))}
            {(!npsStats?.recentMessages || npsStats.recentMessages.length === 0) && (
              <p className="text-center text-gray-500 py-8">
                Nenhuma mensagem NPS encontrada
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
