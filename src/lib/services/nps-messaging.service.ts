import { prisma } from '@/lib/prisma';
import { NpsService } from './nps.service';
import { sendWhatsAppNpsFeedback } from '@/lib/whatsapp';
import { MessageQueue } from '@/lib/message-queue';
import { addMinutes, format } from 'date-fns';

export interface NpsMessageData {
  lessonId: string;
  studentId: string;
  studentName: string;
  studentPhone: string;
  lessonTitle: string;
  courseName: string;
  className: string;
  lessonDate: Date;
}

export class NpsMessagingService {
  private static readonly messageQueue = new MessageQueue();

  /**
   * Schedule NPS feedback messages for all students in a completed lesson
   */
  static async scheduleNpsFeedbackForLesson(lessonId: string, delayMinutes: number = 30): Promise<void> {
    try {
      console.log(`📝 Scheduling NPS feedback messages for lesson ${lessonId}...`);

      // Get lesson with enrolled students
      const lesson = await prisma.lesson.findUnique({
        where: { id: lessonId },
        include: {
          class: {
            include: {
              course: true,
              enrollments: {
                where: {
                  status: 'active'
                },
                include: {
                  student: true
                }
              }
            }
          },
          attendance: {
            where: {
              status: 'present'
            },
            include: {
              student: true
            }
          }
        }
      });

      if (!lesson) {
        throw new Error(`Lesson ${lessonId} not found`);
      }

      // Only send NPS to students who attended the lesson
      const attendedStudents = lesson.attendance.map(att => att.student);
      
      if (attendedStudents.length === 0) {
        console.log(`⚠️ No students attended lesson ${lessonId}, skipping NPS feedback`);
        return;
      }

      console.log(`👥 Found ${attendedStudents.length} students who attended the lesson`);

      // Schedule messages for each student
      const scheduledFor = addMinutes(new Date(), delayMinutes);
      
      for (const student of attendedStudents) {
        if (!student.phone) {
          console.log(`⚠️ Student ${student.name} has no phone number, skipping NPS feedback`);
          continue;
        }

        try {
          await this.scheduleNpsFeedbackForStudent({
            lessonId: lesson.id,
            studentId: student.id,
            studentName: student.name,
            studentPhone: student.phone,
            lessonTitle: lesson.title,
            courseName: lesson.class.course.name,
            className: lesson.class.name,
            lessonDate: lesson.scheduledDate
          }, scheduledFor);

          console.log(`✅ Scheduled NPS feedback for ${student.name} at ${scheduledFor.toLocaleString('pt-BR')}`);
        } catch (error) {
          console.error(`❌ Failed to schedule NPS feedback for student ${student.name}:`, error);
        }
      }

      console.log(`✅ NPS feedback scheduling completed for lesson ${lesson.title}`);
    } catch (error) {
      console.error('❌ Error scheduling NPS feedback for lesson:', error);
      throw error;
    }
  }

  /**
   * Schedule NPS feedback message for a specific student
   */
  static async scheduleNpsFeedbackForStudent(data: NpsMessageData, scheduledFor: Date): Promise<void> {
    try {
      // Generate NPS token
      const tokenData = await NpsService.generateNpsToken(data.lessonId, data.studentId);

      // Create message text
      const messageText = this.buildNpsFeedbackMessage({
        ...data,
        feedbackToken: tokenData.token
      });

      // Add to message queue
      await this.messageQueue.enqueue({
        recipientPhone: data.studentPhone,
        messageText,
        messageType: 'nps',
        priority: 2, // Medium priority
        scheduledFor,
        metadata: {
          lessonId: data.lessonId,
          studentId: data.studentId,
          tokenId: tokenData.id,
          messageType: 'nps_feedback'
        }
      });

      console.log(`📝 NPS feedback message queued for ${data.studentName} (${data.studentPhone})`);
    } catch (error) {
      console.error('❌ Error scheduling NPS feedback for student:', error);
      throw error;
    }
  }

  /**
   * Process pending NPS feedback messages
   */
  static async processPendingNpsMessages(): Promise<{
    processed: number;
    sent: number;
    failed: number;
  }> {
    try {
      console.log('📝 Processing pending NPS feedback messages...');

      const now = new Date();
      
      // Get pending NPS messages that are due
      const pendingMessages = await prisma.messageQueue.findMany({
        where: {
          messageType: 'nps',
          status: 'pending',
          scheduledFor: { lte: now },
          attempts: { lt: 3 } // Max 3 attempts
        },
        orderBy: [
          { priority: 'asc' },
          { scheduledFor: 'asc' }
        ],
        take: 20 // Process in batches
      });

      console.log(`📋 Found ${pendingMessages.length} pending NPS messages to process`);

      let sent = 0;
      let failed = 0;

      for (const message of pendingMessages) {
        try {
          // Mark as processing
          await prisma.messageQueue.update({
            where: { id: message.id },
            data: {
              status: 'processing',
              attempts: message.attempts + 1,
              lastAttemptAt: new Date()
            }
          });

          // Parse metadata
          const metadata = message.metadata ? JSON.parse(message.metadata) : {};

          // Send via WhatsApp
          const result = await sendWhatsAppNpsFeedback(message.recipientPhone, {
            studentName: this.extractStudentNameFromMessage(message.messageText),
            lessonTitle: metadata.lessonTitle || 'Aula',
            courseName: metadata.courseName || 'Curso',
            className: metadata.className || 'Turma',
            lessonDate: metadata.lessonDate || new Date().toLocaleDateString('pt-BR'),
            feedbackToken: metadata.tokenId || ''
          });

          if (result.success) {
            // Mark as sent
            await prisma.messageQueue.update({
              where: { id: message.id },
              data: {
                status: 'sent',
                sentAt: new Date(),
                deliveryStatus: 'delivered'
              }
            });
            sent++;
            console.log(`✅ NPS message sent to ${message.recipientPhone}`);
          } else {
            throw new Error(result.error || 'Failed to send WhatsApp message');
          }
        } catch (error) {
          console.error(`❌ Failed to send NPS message to ${message.recipientPhone}:`, error);
          
          // Mark as failed if max attempts reached
          const maxAttempts = 3;
          if (message.attempts + 1 >= maxAttempts) {
            await prisma.messageQueue.update({
              where: { id: message.id },
              data: {
                status: 'failed',
                errorMessage: error instanceof Error ? error.message : 'Unknown error'
              }
            });
          } else {
            // Reset to pending for retry
            await prisma.messageQueue.update({
              where: { id: message.id },
              data: {
                status: 'pending',
                scheduledFor: addMinutes(new Date(), 5) // Retry in 5 minutes
              }
            });
          }
          failed++;
        }
      }

      const result = {
        processed: pendingMessages.length,
        sent,
        failed
      };

      console.log(`📊 NPS message processing completed:`, result);
      return result;
    } catch (error) {
      console.error('❌ Error processing pending NPS messages:', error);
      throw error;
    }
  }

  /**
   * Build NPS feedback message text
   */
  private static buildNpsFeedbackMessage(data: NpsMessageData & { feedbackToken: string }): string {
    const appUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const feedbackUrl = `${appUrl}/feedback?token=${data.feedbackToken}`;
    const lessonDate = format(data.lessonDate, 'dd/MM/yyyy');

    return `📝 *VoxStudent - Avaliação da Aula*

Olá, ${data.studentName}!

Como foi sua experiência na aula de hoje?

📚 *Curso:* ${data.courseName}
🎓 *Turma:* ${data.className}
📖 *Aula:* ${data.lessonTitle}
📅 *Data:* ${lessonDate}

Sua opinião é muito importante para nós! Clique em uma das opções abaixo para avaliar:

😊 *BOA* - ${feedbackUrl}&rating=good
😐 *NEUTRA* - ${feedbackUrl}&rating=neutral  
😞 *RUIM* - ${feedbackUrl}&rating=bad

Ou acesse o link completo para deixar comentários:
${feedbackUrl}

⏰ *Este link expira em 24 horas.*

Obrigado pelo seu feedback!

---
© ${new Date().getFullYear()} VoxStudent - Sistema de Gestão Educacional`;
  }

  /**
   * Extract student name from message text (helper for processing)
   */
  private static extractStudentNameFromMessage(messageText: string): string {
    const match = messageText.match(/Olá, (.+?)!/);
    return match ? match[1] : 'Aluno';
  }

  /**
   * Clean up old NPS messages and tokens
   */
  static async cleanupOldNpsData(): Promise<{ deletedMessages: number; deletedTokens: number }> {
    try {
      console.log('🧹 Cleaning up old NPS data...');

      // Delete old sent/failed messages (older than 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedMessages = await prisma.messageQueue.deleteMany({
        where: {
          messageType: 'nps',
          status: { in: ['sent', 'failed'] },
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      // Clean up expired tokens
      const deletedTokens = await NpsService.cleanupExpiredTokens();

      console.log(`🧹 Cleanup completed: ${deletedMessages.count} messages, ${deletedTokens} tokens`);
      
      return {
        deletedMessages: deletedMessages.count,
        deletedTokens
      };
    } catch (error) {
      console.error('❌ Error cleaning up old NPS data:', error);
      throw error;
    }
  }
}
